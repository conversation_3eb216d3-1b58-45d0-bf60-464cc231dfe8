{"name": "advanced-discord-bot", "version": "1.0.0", "description": "A comprehensive Discord bot with web dashboard and database integration", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "dashboard": "cd dashboard && npm start", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "jest", "deploy": "npm run build && node dist/deploy-commands.js"}, "keywords": ["discord", "bot", "discord.js", "typescript", "dashboard", "supabase"], "author": "Your Name", "license": "MIT", "dependencies": {"discord.js": "^14.14.1", "@discordjs/builders": "^1.7.0", "@discordjs/rest": "^2.2.0", "discord-api-types": "^0.37.61", "@supabase/supabase-js": "^2.38.4", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "node-cron": "^3.0.3", "axios": "^1.6.2", "moment": "^2.29.4", "canvas": "^2.11.2", "sharp": "^0.32.6", "ytdl-core": "^4.11.5", "@discordjs/voice": "^0.16.1", "ffmpeg-static": "^5.2.0", "play-dl": "^1.9.7", "ms": "^2.1.3", "chalk": "^4.1.2"}, "devDependencies": {"@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/ms": "^0.7.34", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}